#!/usr/bin/env python3
"""
Qwen LLM Platform API 使用示例
展示所有主要功能的使用方法
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any

class QwenAPIExamples:
    """Qwen API 使用示例"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def print_example(self, title: str, description: str = ""):
        """打印示例标题"""
        print(f"\n{'='*60}")
        print(f"📚 {title}")
        if description:
            print(f"💡 {description}")
        print('='*60)
    
    def print_result(self, result: Dict[str, Any], response_time: float = 0):
        """打印结果"""
        print(f"⏱️  响应时间: {response_time:.2f}s")
        print(f"📄 结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    
    async def example_basic_chat(self):
        """基础对话示例"""
        self.print_example("基础对话", "最简单的对话功能")
        
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/chat",
            json={
                "message": "你好，请介绍一下你自己",
                "temperature": 0.7,
                "max_tokens": 200
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        else:
            print(f"❌ 请求失败: {response.status_code}")
    
    async def example_document_summary(self):
        """文档摘要示例"""
        self.print_example("文档摘要", "对长文档进行智能摘要")
        
        long_document = """
        随着人工智能技术的快速发展，机器学习已经成为现代科技领域的重要组成部分。
        深度学习作为机器学习的一个分支，通过模拟人脑神经网络的工作原理，
        在图像识别、自然语言处理、语音识别等领域取得了突破性进展。
        
        卷积神经网络（CNN）在计算机视觉任务中表现出色，能够自动提取图像特征。
        循环神经网络（RNN）和长短期记忆网络（LSTM）在处理序列数据方面具有优势。
        Transformer架构的出现更是革命性地改变了自然语言处理领域，
        催生了GPT、BERT等大型语言模型。
        
        这些技术的应用已经深入到我们生活的方方面面，从智能手机的语音助手，
        到自动驾驶汽车，再到医疗诊断系统，人工智能正在改变着世界。
        """
        
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/summarize",
            json={
                "text": long_document,
                "max_length": 100
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        else:
            print(f"❌ 请求失败: {response.status_code}")
    
    async def example_translation(self):
        """翻译示例"""
        self.print_example("文本翻译", "多语言翻译功能")
        
        # 英译中
        print("\n🔄 英文翻译为中文:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/translate",
            json={
                "text": "Artificial Intelligence is transforming the world in unprecedented ways.",
                "target_language": "zh"
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        
        # 中译英
        print("\n🔄 中文翻译为英文:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/translate",
            json={
                "text": "机器学习是人工智能的核心技术之一",
                "target_language": "en"
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
    
    async def example_oa_integration(self):
        """OA系统集成示例"""
        self.print_example("OA系统集成", "智能审批分析")
        
        approval_document = """
        申请人：张三
        部门：技术部
        职位：高级工程师
        申请类型：年假申请
        申请时间：2024年1月15日 - 2024年1月25日
        申请天数：10天
        申请原因：春节回家探亲，已提前安排好工作交接
        紧急联系方式：13800138000
        """
        
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/oa/approval",
            json={
                "document_content": approval_document,
                "approval_type": "leave",
                "context": {
                    "department": "技术部",
                    "employee_level": "高级工程师"
                }
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        else:
            print(f"❌ 请求失败: {response.status_code}")
    
    async def example_meeting_integration(self):
        """会议集成示例"""
        self.print_example("会议集成", "会议记录智能分析")
        
        meeting_transcript = """
        张三: 大家好，今天我们讨论Q1季度的产品发布计划。
        李四: 根据市场调研，我们的新产品很有竞争力，建议提前发布。
        王五: 但是测试团队反馈还有几个关键bug需要修复，可能需要延期两周。
        张三: 那我们需要权衡一下市场时机和产品质量。
        李四: 我建议先发布核心功能，其他功能后续更新。
        王五: 这样可以，但需要确保核心功能的稳定性。
        张三: 好的，那就这样决定。李四负责市场推广准备，王五负责核心功能测试，下周五前给出最终方案。
        """
        
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/meeting/summarize",
            json={
                "transcript": meeting_transcript,
                "meeting_type": "product_planning",
                "participants": ["张三", "李四", "王五"],
                "meeting_info": {
                    "title": "Q1产品发布计划讨论",
                    "date": "2024-01-10",
                    "duration": "60分钟"
                }
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        else:
            print(f"❌ 请求失败: {response.status_code}")
    
    async def example_education_integration(self):
        """教育集成示例"""
        self.print_example("教育集成", "智能作业批改")
        
        # 作业批改示例
        print("\n📝 作业批改:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/education/grade",
            json={
                "assignment_text": "请解释牛顿第一定律的含义和应用",
                "student_answer": "牛顿第一定律又称惯性定律，指出物体在没有外力作用时，会保持静止状态或匀速直线运动状态。这个定律解释了为什么汽车突然刹车时乘客会向前倾斜。",
                "subject": "物理",
                "grade_level": "高中",
                "max_score": 10
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        
        # 智能答疑示例
        print("\n❓ 智能答疑:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/education/qa",
            json={
                "question": "为什么水在0度会结冰？",
                "subject": "化学",
                "grade_level": "初中",
                "context": "我们正在学习物质的三态变化"
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
    
    async def example_email_integration(self):
        """邮件集成示例"""
        self.print_example("邮件集成", "智能邮件处理")
        
        # 邮件分类
        print("\n📧 邮件分类:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/email/classify",
            json={
                "subject": "紧急：服务器故障需要立即处理",
                "content": "生产环境服务器出现异常，用户无法正常访问，请技术团队立即查看并处理。",
                "sender": "<EMAIL>",
                "received_time": "2024-01-10T14:30:00Z"
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        
        # 邮件回复生成
        print("\n✉️  邮件回复生成:")
        start_time = time.time()
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/email/reply",
            json={
                "original_email": "您好，请问贵公司的产品价格和技术支持服务如何？我们正在评估合作可能性。",
                "reply_type": "business",
                "tone": "professional",
                "key_points": ["产品价格", "技术支持", "合作方案"]
            }
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
    
    async def example_performance_monitoring(self):
        """性能监控示例"""
        self.print_example("性能监控", "系统状态和性能统计")
        
        # 健康检查
        print("\n🏥 健康检查:")
        start_time = time.time()
        response = await self.client.get(f"{self.base_url}/health")
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
        
        # 性能统计
        print("\n📊 性能统计:")
        start_time = time.time()
        response = await self.client.get(f"{self.base_url}/stats")
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            self.print_result(result, response_time)
    
    async def run_all_examples(self):
        """运行所有示例"""
        print("🎭 Qwen LLM Platform API 使用示例")
        print("展示所有主要功能的使用方法")
        
        examples = [
            self.example_basic_chat,
            self.example_document_summary,
            self.example_translation,
            self.example_oa_integration,
            self.example_meeting_integration,
            self.example_education_integration,
            self.example_email_integration,
            self.example_performance_monitoring
        ]
        
        for example in examples:
            try:
                await example()
                await asyncio.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                print(f"❌ 示例执行失败: {str(e)}")
        
        print(f"\n🎉 所有示例执行完成!")


async def main():
    """主函数"""
    print("🚀 启动 Qwen LLM Platform API 示例")
    
    # 检查服务状态
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health", timeout=5.0)
            if response.status_code != 200:
                print("❌ 服务未正常运行，请先启动服务")
                print("💡 启动命令: python src/main.py")
                return
    except:
        print("❌ 无法连接到服务，请先启动服务")
        print("💡 启动命令: python src/main.py")
        return
    
    # 运行示例
    async with QwenAPIExamples() as examples:
        await examples.run_all_examples()


if __name__ == "__main__":
    asyncio.run(main())

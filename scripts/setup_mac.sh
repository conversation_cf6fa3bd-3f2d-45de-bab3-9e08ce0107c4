#!/bin/bash

# Qwen LLM Platform - Mac M2 Pro 环境安装脚本
# 适用于 Apple Silicon (M1/M2) Mac

set -e

echo "🚀 开始安装 Qwen LLM Platform 开发环境..."

# 检查系统架构
ARCH=$(uname -m)
if [[ "$ARCH" != "arm64" ]]; then
    echo "❌ 此脚本仅适用于 Apple Silicon (M1/M2) Mac"
    exit 1
fi

# 检查 macOS 版本
MACOS_VERSION=$(sw_vers -productVersion)
echo "📱 检测到 macOS 版本: $MACOS_VERSION"

# 1. 检查并安装 Homebrew
if ! command -v brew &> /dev/null; then
    echo "📦 安装 Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
    eval "$(/opt/homebrew/bin/brew shellenv)"
else
    echo "✅ Homebrew 已安装"
fi

# 2. 安装基础依赖
echo "🔧 安装基础依赖..."
brew update
brew install python@3.11 git cmake wget

# 3. 创建 Python 虚拟环境
echo "🐍 创建 Python 虚拟环境..."
python3.11 -m venv venv
source venv/bin/activate

# 4. 升级 pip
echo "📦 升级 pip..."
pip install --upgrade pip

# 5. 安装 Python 依赖
echo "📚 安装 Python 依赖..."
pip install fastapi uvicorn[standard] pydantic requests aiofiles --trusted-host pypi.org --trusted-host files.pythonhosted.org
pip install llama-cpp-python --extra-index-url https://abetlen.github.io/llama-cpp-python/whl/metal --trusted-host abetlen.github.io --trusted-host pypi.org --trusted-host files.pythonhosted.org

# 6. 编译 llama.cpp (针对 Apple Silicon 优化)
echo "⚡ 编译 llama.cpp..."
if [ ! -d "llama.cpp" ]; then
    git clone https://github.com/ggerganov/llama.cpp.git
fi

cd llama.cpp
make clean
# 使用 Metal 加速 (Apple GPU)
make LLAMA_METAL=1 -j$(sysctl -n hw.ncpu)
cd ..

# 7. 创建必要目录
echo "📁 创建项目目录..."
mkdir -p models logs data

# 8. 设置环境变量
echo "🔧 配置环境变量..."
cat >> .env << EOF
# Qwen LLM Platform 配置
MODEL_PATH=./models
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
MAX_TOKENS=2048
TEMPERATURE=0.7
EOF

echo "✅ Mac M2 Pro 环境安装完成！"
echo ""
echo "🎯 下一步操作："
echo "1. 激活虚拟环境: source venv/bin/activate"
echo "2. 下载模型: ./scripts/download_model.sh"
echo "3. 启动服务: python src/main.py"
echo ""
echo "📖 更多信息请查看 README.md"

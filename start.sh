#!/bin/bash

# Qwen LLM Platform 快速启动脚本

set -e

echo "🚀 Qwen LLM Platform 快速启动"
echo "================================"

# 检查操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    PLATFORM="mac"
    echo "📱 检测到 macOS 系统"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PLATFORM="linux"
    echo "🐧 检测到 Linux 系统"
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

# 检查是否首次运行
if [ ! -f ".initialized" ]; then
    echo "🔧 首次运行，开始初始化..."
    
    if [ "$PLATFORM" == "mac" ]; then
        echo "📦 运行 Mac 安装脚本..."
        ./scripts/setup_mac.sh
    else
        echo "📦 请手动运行服务器部署脚本: ./scripts/deploy_server.sh"
        exit 1
    fi
    
    echo "🤖 下载模型..."
    ./scripts/download_model.sh
    
    # 标记已初始化
    touch .initialized
    echo "✅ 初始化完成！"
fi

# 检查虚拟环境
if [ "$PLATFORM" == "mac" ]; then
    if [ ! -d "venv" ]; then
        echo "❌ 虚拟环境不存在，请先运行: ./scripts/setup_mac.sh"
        exit 1
    fi
    
    echo "🐍 激活虚拟环境..."
    source venv/bin/activate
fi

# 检查模型文件
if [ ! -f "models/model_config.json" ]; then
    echo "❌ 模型配置不存在"
    echo ""
    echo "🤖 请选择模型下载方式："
    echo "1) 标准下载 (推荐)"
    echo "2) 使用 Hugging Face CLI (更稳定)"
    echo "3) 下载小模型 (快速测试)"
    echo "4) 手动下载指导"
    echo ""

    read -p "请选择 (1-4): " download_choice

    case $download_choice in
        1)
            echo "🔄 运行标准下载..."
            ./scripts/download_model_alternative.sh
            ;;
        2)
            echo "🔄 使用 Hugging Face CLI 下载..."
            ./scripts/download_with_hf_cli.sh
            ;;
        3)
            echo "🔄 下载小模型..."
            ./scripts/download_small_model.sh
            ;;
        4)
            echo "📖 手动下载指导："
            echo "1. 访问 https://huggingface.co/Qwen/Qwen1.5-4B-Chat-GGUF"
            echo "2. 下载 qwen1_5-4b-chat-q4_k_m.gguf"
            echo "3. 放置到 models/ 目录"
            echo "4. 重新运行此脚本"
            exit 1
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac

    # 检查下载是否成功
    if [ ! -f "models/model_config.json" ]; then
        echo "❌ 模型下载失败，请手动下载或检查网络连接"
        exit 1
    fi
fi

# 检查依赖
echo "📚 检查 Python 依赖..."
if [ "$PLATFORM" == "mac" ]; then
    pip install -r requirements.txt > /dev/null 2>&1 || {
        echo "⚠️  安装依赖失败，请检查 requirements.txt"
    }
fi

# 启动选项
echo ""
echo "🎯 请选择启动方式："
echo "1) 开发模式 (本地 Python)"
echo "2) Docker 模式"
echo "3) 仅测试模型"
echo "4) 查看系统状态"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 启动开发模式..."
        echo "📖 API 文档: http://localhost:8000/docs"
        echo "🔍 健康检查: http://localhost:8000/health"
        echo ""
        echo "按 Ctrl+C 停止服务"
        echo ""
        
        cd src
        python main.py
        ;;
    
    2)
        echo "🐳 启动 Docker 模式..."
        
        if ! command -v docker &> /dev/null; then
            echo "❌ Docker 未安装，请先安装 Docker"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
            exit 1
        fi
        
        echo "🔨 构建 Docker 镜像..."
        docker-compose build
        
        echo "🚀 启动容器..."
        docker-compose up -d
        
        echo "⏳ 等待服务启动..."
        sleep 10
        
        echo "🧪 测试服务..."
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            echo "✅ 服务启动成功！"
            echo "📖 API 文档: http://localhost:8000/docs"
            echo "🔍 健康检查: http://localhost:8000/health"
            echo ""
            echo "管理命令："
            echo "  查看日志: docker-compose logs -f"
            echo "  停止服务: docker-compose down"
        else
            echo "❌ 服务启动失败，查看日志:"
            docker-compose logs
        fi
        ;;
    
    3)
        echo "🧪 测试模型..."
        
        if [ ! -f "llama.cpp/main" ]; then
            echo "❌ llama.cpp 未编译，请先运行安装脚本"
            exit 1
        fi
        
        MODEL_FILE=$(jq -r '.model_file' models/model_config.json)
        
        if [ ! -f "models/$MODEL_FILE" ]; then
            echo "❌ 模型文件不存在: models/$MODEL_FILE"
            exit 1
        fi
        
        echo "🤖 测试模型推理..."
        echo "你好，请简单介绍一下你自己。" | ./llama.cpp/main \
            -m "models/$MODEL_FILE" \
            -n 100 \
            -p "你好，请简单介绍一下你自己。" \
            --temp 0.7
        ;;
    
    4)
        echo "📊 系统状态检查..."
        echo ""
        
        # 检查文件
        echo "📁 文件检查:"
        [ -f "models/model_config.json" ] && echo "  ✅ 模型配置存在" || echo "  ❌ 模型配置缺失"
        [ -d "models" ] && echo "  ✅ 模型目录存在" || echo "  ❌ 模型目录缺失"
        [ -f "llama.cpp/main" ] && echo "  ✅ llama.cpp 已编译" || echo "  ❌ llama.cpp 未编译"
        
        if [ "$PLATFORM" == "mac" ]; then
            [ -d "venv" ] && echo "  ✅ 虚拟环境存在" || echo "  ❌ 虚拟环境缺失"
        fi
        
        echo ""
        
        # 检查模型文件
        if [ -f "models/model_config.json" ]; then
            echo "🤖 模型信息:"
            MODEL_NAME=$(jq -r '.model_name' models/model_config.json)
            MODEL_FILE=$(jq -r '.model_file' models/model_config.json)
            echo "  模型名称: $MODEL_NAME"
            echo "  模型文件: $MODEL_FILE"
            
            if [ -f "models/$MODEL_FILE" ]; then
                MODEL_SIZE=$(du -h "models/$MODEL_FILE" | cut -f1)
                echo "  文件大小: $MODEL_SIZE"
                echo "  ✅ 模型文件完整"
            else
                echo "  ❌ 模型文件缺失"
            fi
        fi
        
        echo ""
        
        # 检查服务状态
        echo "🌐 服务状态:"
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            echo "  ✅ API 服务运行中"
            echo "  📖 API 文档: http://localhost:8000/docs"
        else
            echo "  ❌ API 服务未运行"
        fi
        
        # 检查 Docker
        if command -v docker &> /dev/null; then
            echo "  ✅ Docker 已安装"
            
            if docker ps | grep -q "qwen-llm"; then
                echo "  ✅ Docker 容器运行中"
            else
                echo "  ❌ Docker 容器未运行"
            fi
        else
            echo "  ❌ Docker 未安装"
        fi
        ;;
    
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
